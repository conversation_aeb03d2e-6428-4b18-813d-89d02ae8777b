import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import {
  Shield,
  Users,
  Globe,
  Award,
  Heart,
  Target,
  Sparkles,
  TrendingUp,
  Clock,
  MapPin,
  Calendar,
  Star,
  CheckCircle,
  ArrowRight,
  ExternalLink,
  Building,
  Zap,
  Eye,
  Lightbulb,
  Handshake,
  BookOpen,
  Trophy,
  Medal,
  Briefcase,
  GraduationCap,
  Mail,
  Linkedin,
  Twitter,
  Phone,
  Quote,
  Play,
  Download,
  FileText,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';

const About: React.FC = () => {
  const [activeTab, setActiveTab] = useState('mission');

  const impactStats = [
    { value: "2.3M", label: "Lives Helped", icon: Users, color: "from-blue-500 to-indigo-600" },
    { value: "156", label: "Countries Served", icon: Globe, color: "from-emerald-500 to-green-600" },
    { value: "24/7", label: "Emergency Response", icon: Clock, color: "from-orange-500 to-red-600" },
    { value: "98%", label: "Response Rate", icon: TrendingUp, color: "from-purple-500 to-pink-600" }
  ];

  const timeline = [
    {
      year: "2018",
      title: "Foundation",
      description: "DisasterWatch was founded with a vision to revolutionize disaster response through technology.",
      milestone: "Company Founded"
    },
    {
      year: "2019",
      title: "First Platform Launch",
      description: "Launched our initial disaster reporting platform, serving 5 countries in Southeast Asia.",
      milestone: "Platform Launch"
    },
    {
      year: "2020",
      title: "Global Expansion",
      description: "Expanded operations to 50+ countries during the COVID-19 pandemic response.",
      milestone: "Global Reach"
    },
    {
      year: "2021",
      title: "AI Integration",
      description: "Integrated advanced AI and machine learning for predictive disaster analytics.",
      milestone: "AI Technology"
    },
    {
      year: "2022",
      title: "Partnership Network",
      description: "Established partnerships with UN, Red Cross, and major humanitarian organizations.",
      milestone: "Strategic Partnerships"
    },
    {
      year: "2023",
      title: "Recognition & Awards",
      description: "Received multiple international awards for innovation in disaster management technology.",
      milestone: "Industry Recognition"
    }
  ];

  const teamMembers = [
    {
      name: "Dr. Sarah Chen",
      role: "CEO & Co-Founder",
      bio: "Former UN disaster response coordinator with 15+ years in emergency management.",
      image: "/api/placeholder/300/300",
      education: "PhD Emergency Management, Harvard",
      linkedin: "#",
      twitter: "#"
    },
    {
      name: "Michael Rodriguez",
      role: "CTO & Co-Founder",
      bio: "Tech veteran with expertise in AI, machine learning, and large-scale systems.",
      image: "/api/placeholder/300/300",
      education: "MS Computer Science, MIT",
      linkedin: "#",
      twitter: "#"
    },
    {
      name: "Dr. Amara Okafor",
      role: "Chief Medical Officer",
      bio: "Emergency medicine physician specializing in disaster medical response.",
      image: "/api/placeholder/300/300",
      education: "MD, Johns Hopkins",
      linkedin: "#",
      twitter: "#"
    },
    {
      name: "James Thompson",
      role: "Head of Operations",
      bio: "Former FEMA coordinator with extensive field experience in disaster response.",
      image: "/api/placeholder/300/300",
      education: "MBA Public Administration, Georgetown",
      linkedin: "#",
      twitter: "#"
    }
  ];

  const values = [
    {
      icon: Heart,
      title: "Compassion",
      description: "We approach every situation with empathy, recognizing the human impact behind every disaster report.",
      color: "from-red-500 to-pink-500"
    },
    {
      icon: Target,
      title: "Precision",
      description: "Accurate, verified information is crucial for effective disaster response and saving lives.",
      color: "from-blue-500 to-indigo-500"
    },
    {
      icon: Users,
      title: "Collaboration",
      description: "We believe in the power of collective action and bringing communities together.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "Continuously advancing technology to improve disaster preparedness and response.",
      color: "from-yellow-500 to-orange-500"
    },
    {
      icon: Shield,
      title: "Integrity",
      description: "Maintaining the highest ethical standards in all our operations and partnerships.",
      color: "from-purple-500 to-violet-500"
    },
    {
      icon: Globe,
      title: "Global Impact",
      description: "Working towards a world where communities are resilient and prepared for disasters.",
      color: "from-teal-500 to-cyan-500"
    }
  ];

  const testimonials = [
    {
      name: "Dr. Maria Santos",
      role: "UN Disaster Risk Reduction",
      quote: "DisasterWatch has transformed how we coordinate international disaster response. Their platform saves precious time when every minute counts.",
      image: "/api/placeholder/80/80",
      organization: "United Nations"
    },
    {
      name: "Robert Kim",
      role: "Emergency Management Director",
      quote: "The real-time data and analytics provided by DisasterWatch have been instrumental in our disaster preparedness strategies.",
      image: "/api/placeholder/80/80",
      organization: "Red Cross International"
    },
    {
      name: "Lisa Johnson",
      role: "Community Response Coordinator",
      quote: "This platform has empowered our local communities to respond more effectively to emergencies and support each other.",
      image: "/api/placeholder/80/80",
      organization: "FEMA"
    }
  ];

  const achievements = [
    {
      icon: Trophy,
      title: "UN Innovation Award 2023",
      description: "Recognized for outstanding contribution to disaster risk reduction technology"
    },
    {
      icon: Medal,
      title: "Tech for Good Award",
      description: "Winner of the global technology for social impact competition"
    },
    {
      icon: Star,
      title: "ISO 27001 Certified",
      description: "International standard for information security management systems"
    },
    {
      icon: Award,
      title: "B-Corp Certification",
      description: "Certified as a business that meets high standards of social and environmental impact"
    }
  ];

  const partnerships = [
    { name: "United Nations", logo: "/api/placeholder/120/60" },
    { name: "Red Cross", logo: "/api/placeholder/120/60" },
    { name: "FEMA", logo: "/api/placeholder/120/60" },
    { name: "WHO", logo: "/api/placeholder/120/60" },
    { name: "World Bank", logo: "/api/placeholder/120/60" },
    { name: "USAID", logo: "/api/placeholder/120/60" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/20">
      <Header />

      <main className="pt-16">
        {/* Enhanced Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900"></div>
          <div className="absolute inset-0 bg-black/20"></div>

          {/* Floating decorative elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400/30 to-purple-500/30 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-br from-pink-400/30 to-red-500/30 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-gradient-to-br from-emerald-400/30 to-green-500/30 rounded-full blur-xl animate-pulse delay-500"></div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-sm font-semibold mb-8">
                <Sparkles size={18} className="mr-2 text-yellow-400" />
                Transforming Disaster Response Since 2018
              </div>

              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight">
                About
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-300 to-pink-300">
                  DisasterWatch
                </span>
              </h1>

              <p className="text-xl sm:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed mb-12 font-medium">
                We're building the world's most advanced disaster management platform to connect communities,
                coordinate responses, and save lives through intelligent technology and human compassion.
              </p>

              {/* Quick Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <Link
                  to="/contact"
                  className="group bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  <span className="relative flex items-center justify-center">
                    Join Our Mission
                    <ArrowRight size={20} className="ml-2" />
                  </span>
                </Link>
                <button
                  onClick={() => document.getElementById('our-story')?.scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white/10 backdrop-blur-sm text-white px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white/20 transition-all duration-300 border border-white/30 flex items-center justify-center"
                >
                  <Play size={20} className="mr-2" />
                  Our Story
                </button>
              </div>
            </div>

            {/* Impact Statistics */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {impactStats.map((stat, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 text-center hover:bg-white/15 transition-all duration-300">
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mb-6 mx-auto`}>
                    <stat.icon size={32} className="text-white" />
                  </div>
                  <div className="text-4xl font-black text-white mb-2">{stat.value}</div>
                  <div className="text-blue-200 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission, Vision & Story Section */}
        <section id="our-story" className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Tab Navigation */}
            <div className="flex justify-center mb-16">
              <div className="bg-gray-100 rounded-2xl p-2 inline-flex">
                {[
                  { id: 'mission', label: 'Our Mission', icon: Target },
                  { id: 'vision', label: 'Our Vision', icon: Eye },
                  { id: 'story', label: 'Our Story', icon: BookOpen }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-8 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center ${
                      activeTab === tab.id
                        ? 'bg-white text-blue-600 shadow-lg'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <tab.icon size={20} className="mr-2" />
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                {activeTab === 'mission' && (
                  <div className="space-y-6">
                    <h2 className="text-4xl font-black text-gray-900 mb-6">Our Mission</h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      DisasterWatch was founded with a simple yet powerful mission: to revolutionize how communities
                      prepare for, respond to, and recover from disasters. We believe that technology can bridge the
                      gap between those who need help and those who can provide it.
                    </p>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      Our platform empowers individuals, organizations, and governments to work together seamlessly
                      during critical moments when every second counts.
                    </p>
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                      <div className="flex items-center mb-3">
                        <Target size={24} className="text-blue-600 mr-3" />
                        <h3 className="text-lg font-bold text-blue-900">Core Mission</h3>
                      </div>
                      <p className="text-blue-800 font-medium">
                        "To save lives and reduce suffering by connecting communities with the resources and information they need during disasters."
                      </p>
                    </div>
                  </div>
                )}

                {activeTab === 'vision' && (
                  <div className="space-y-6">
                    <h2 className="text-4xl font-black text-gray-900 mb-6">Our Vision</h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      We envision a world where every community is resilient, prepared, and connected. A world where
                      disasters don't have to mean devastation, and where technology serves humanity's greatest needs.
                    </p>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      Through innovation, collaboration, and unwavering commitment to human welfare, we're building
                      the infrastructure for a safer, more responsive world.
                    </p>
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200">
                      <div className="flex items-center mb-3">
                        <Eye size={24} className="text-purple-600 mr-3" />
                        <h3 className="text-lg font-bold text-purple-900">Vision 2030</h3>
                      </div>
                      <p className="text-purple-800 font-medium">
                        "A globally connected disaster response network that prevents tragedies and builds resilient communities worldwide."
                      </p>
                    </div>
                  </div>
                )}

                {activeTab === 'story' && (
                  <div className="space-y-6">
                    <h2 className="text-4xl font-black text-gray-900 mb-6">Our Story</h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      DisasterWatch began in 2018 when our founders witnessed firsthand the communication breakdowns
                      during a major earthquake response. They realized that while we had incredible technology,
                      it wasn't being used effectively to save lives during disasters.
                    </p>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      What started as a simple idea to connect disaster victims with help has grown into a
                      comprehensive platform serving millions of people across 156 countries.
                    </p>
                    <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200">
                      <div className="flex items-center mb-3">
                        <BookOpen size={24} className="text-emerald-600 mr-3" />
                        <h3 className="text-lg font-bold text-emerald-900">Founding Moment</h3>
                      </div>
                      <p className="text-emerald-800 font-medium">
                        "Every disaster teaches us something new. Our job is to learn faster than disasters can surprise us."
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Visual Elements */}
              <div className="grid grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-3xl border border-blue-200 hover:shadow-xl hover:-translate-y-1 transition-all duration-500">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-6">
                    <Shield size={32} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Safety First</h3>
                  <p className="text-gray-600 leading-relaxed">Prioritizing community safety in every decision we make</p>
                </div>
                <div className="bg-gradient-to-br from-emerald-50 to-green-50 p-8 rounded-3xl border border-emerald-200 hover:shadow-xl hover:-translate-y-1 transition-all duration-500">
                  <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mb-6">
                    <Users size={32} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Community</h3>
                  <p className="text-gray-600 leading-relaxed">Connecting people when they need it most</p>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-8 rounded-3xl border border-purple-200 hover:shadow-xl hover:-translate-y-1 transition-all duration-500">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center mb-6">
                    <Globe size={32} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Global Impact</h3>
                  <p className="text-gray-600 leading-relaxed">Making a difference worldwide, one community at a time</p>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-red-50 p-8 rounded-3xl border border-orange-200 hover:shadow-xl hover:-translate-y-1 transition-all duration-500">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6">
                    <Award size={32} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Excellence</h3>
                  <p className="text-gray-600 leading-relaxed">Committed to the highest standards of service</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Values Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Core Values</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                These fundamental principles guide everything we do and shape how we serve communities worldwide,
                ensuring our mission remains focused on human welfare and technological excellence.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <div key={index} className="group bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 relative overflow-hidden">
                  {/* Background gradient on hover */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${value.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>

                  <div className="relative">
                    {/* Icon */}
                    <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <value.icon size={32} className="text-white" />
                    </div>

                    {/* Content */}
                    <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-gray-800">{value.title}</h3>
                    <p className="text-gray-600 leading-relaxed group-hover:text-gray-700">
                      {value.description}
                    </p>

                    {/* Decorative element */}
                    <div className={`absolute top-6 right-6 w-2 h-2 bg-gradient-to-r ${value.color} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Values in Action */}
            <div className="mt-20 bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 p-12">
              <div className="text-center mb-12">
                <h3 className="text-3xl font-bold text-gray-900 mb-4">Values in Action</h3>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  See how our values translate into real-world impact and meaningful change in disaster response.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Activity size={40} className="text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Real-Time Response</h4>
                  <p className="text-gray-600">Our values drive us to provide immediate, accurate disaster response coordination 24/7.</p>
                </div>

                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Handshake size={40} className="text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Community Partnership</h4>
                  <p className="text-gray-600">We build lasting relationships with local communities and international organizations.</p>
                </div>

                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Zap size={40} className="text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Innovation Impact</h4>
                  <p className="text-gray-600">Continuous technological advancement ensures we stay ahead of emerging challenges.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Journey</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                From a simple idea to a global platform serving millions - discover the key milestones
                that shaped DisasterWatch into what it is today.
              </p>
            </div>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>

              <div className="space-y-16">
                {timeline.map((item, index) => (
                  <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-12 text-right' : 'pl-12 text-left'}`}>
                      <div className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4">
                          {item.milestone}
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{item.description}</p>
                      </div>
                    </div>

                    {/* Timeline node */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                      <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <Calendar size={16} className="text-blue-600" />
                      </div>
                    </div>

                    <div className={`w-1/2 ${index % 2 === 0 ? 'pl-12 text-left' : 'pr-12 text-right'}`}>
                      <div className="text-4xl font-black text-blue-600 mb-2">{item.year}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Team Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Meet Our Leadership Team</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our diverse team of experts brings together decades of experience in emergency management,
                technology, and humanitarian work to drive our mission forward.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {teamMembers.map((member, index) => (
                <div key={index} className="group bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                  {/* Profile Image */}
                  <div className="relative overflow-hidden">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Social Links */}
                    <div className="absolute bottom-4 left-4 right-4 flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <a href={member.linkedin} className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                        <Linkedin size={18} className="text-blue-600" />
                      </a>
                      <a href={member.twitter} className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                        <Twitter size={18} className="text-blue-400" />
                      </a>
                      <a href="mailto:" className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                        <Mail size={18} className="text-gray-600" />
                      </a>
                    </div>
                  </div>

                  {/* Profile Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <div className="text-blue-600 font-semibold mb-3">{member.role}</div>
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">{member.bio}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <GraduationCap size={14} className="mr-2" />
                      {member.education}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Team Stats */}
            <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 p-12">
              <div className="grid md:grid-cols-4 gap-8 text-center">
                <div>
                  <div className="text-4xl font-black text-blue-600 mb-2">50+</div>
                  <div className="text-gray-600 font-medium">Team Members</div>
                </div>
                <div>
                  <div className="text-4xl font-black text-emerald-600 mb-2">15+</div>
                  <div className="text-gray-600 font-medium">Countries</div>
                </div>
                <div>
                  <div className="text-4xl font-black text-purple-600 mb-2">25+</div>
                  <div className="text-gray-600 font-medium">Languages</div>
                </div>
                <div>
                  <div className="text-4xl font-black text-orange-600 mb-2">100+</div>
                  <div className="text-gray-600 font-medium">Years Combined Experience</div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Testimonials Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">What Partners Say</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Hear from the organizations and leaders who work with us to make disaster response more effective.
              </p>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 relative">
                  {/* Quote icon */}
                  <div className="absolute top-6 right-6 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Quote size={24} className="text-blue-600" />
                  </div>

                  <div className="flex items-center mb-6">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-16 h-16 rounded-full object-cover mr-4"
                    />
                    <div>
                      <div className="font-bold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                      <div className="text-sm text-blue-600 font-medium">{testimonial.organization}</div>
                    </div>
                  </div>

                  <blockquote className="text-gray-700 italic leading-relaxed mb-4">
                    "{testimonial.quote}"
                  </blockquote>

                  <div className="flex items-center">
                    {Array.from({ length: 5 }, (_, i) => (
                      <Star key={i} size={16} className="text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Achievements & Recognition */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Recognition & Achievements</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our commitment to excellence has been recognized by leading organizations worldwide.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {achievements.map((achievement, index) => (
                <div key={index} className="bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 p-8 text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                  <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <achievement.icon size={32} className="text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{achievement.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{achievement.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Partnerships Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Trusted Partners</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We collaborate with leading international organizations to maximize our global impact.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
              {partnerships.map((partner, index) => (
                <div key={index} className="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300 flex items-center justify-center">
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className="max-h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Transparency & Reports */}
        <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Transparency & Accountability</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We believe in complete transparency about our operations, impact, and financial stewardship.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 text-center hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <FileText size={32} className="text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Annual Report 2023</h3>
                <p className="text-gray-600 mb-6">Comprehensive overview of our impact, operations, and financial performance.</p>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-2xl font-semibold hover:bg-blue-700 transition-colors flex items-center mx-auto">
                  <Download size={18} className="mr-2" />
                  Download PDF
                </button>
              </div>

              <div className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 text-center hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <BarChart3 size={32} className="text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Impact Dashboard</h3>
                <p className="text-gray-600 mb-6">Real-time metrics and analytics showing our global disaster response impact.</p>
                <button className="bg-emerald-600 text-white px-6 py-3 rounded-2xl font-semibold hover:bg-emerald-700 transition-colors flex items-center mx-auto">
                  <ExternalLink size={18} className="mr-2" />
                  View Dashboard
                </button>
              </div>

              <div className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 text-center hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <PieChart size={32} className="text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Financial Transparency</h3>
                <p className="text-gray-600 mb-6">Detailed breakdown of how donations and funding are utilized for maximum impact.</p>
                <button className="bg-purple-600 text-white px-6 py-3 rounded-2xl font-semibold hover:bg-purple-700 transition-colors flex items-center mx-auto">
                  <FileText size={18} className="mr-2" />
                  View Financials
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Final Call to Action */}
        <section className="py-20 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-0 left-0 w-40 h-40 bg-white/10 rounded-full -translate-x-20 -translate-y-20 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white/10 rounded-full translate-x-16 translate-y-16 animate-pulse delay-1000"></div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl sm:text-5xl font-black text-white mb-6">
              Join Our Mission
            </h2>
            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Whether you're an individual, organization, or government agency, there are many ways to get involved
              and help us build a more resilient world. Together, we can save lives and strengthen communities.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/donate"
                className="group bg-white text-blue-600 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl"
              >
                <span className="flex items-center justify-center">
                  <Heart size={20} className="mr-2" />
                  Support Our Work
                </span>
              </Link>
              <Link
                to="/contact"
                className="bg-white/10 backdrop-blur-sm text-white px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white/20 transition-all duration-300 border border-white/30 flex items-center justify-center"
              >
                Partner With Us
                <ArrowRight size={20} className="ml-2" />
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default About;
