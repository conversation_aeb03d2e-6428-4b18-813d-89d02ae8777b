import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import Home from './pages/Home';
import Reports from './pages/Reports';
import ReportDetail from './pages/ReportDetail';
import ReportImpact from './pages/ReportImpact';
import AdminPanel from './pages/admin/AdminPanel';
import About from './pages/About';
import WhatWeDo from './pages/WhatWeDo';
import GetInvolved from './pages/GetInvolved';
import News from './pages/News';
import Resources from './pages/Resources';
import Contact from './pages/Contact';


import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<LoginPage />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              {/* Public routes */}
              <Route path="/about" element={<About />} />
              <Route path="/what-we-do" element={<WhatWeDo />} />
              <Route path="/get-involved" element={<GetInvolved />} />
              <Route path="/news" element={<News />} />
              <Route path="/resources" element={<Resources />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/donate" element={<div className="p-8 text-center">Donate page coming soon...</div>} />
              {/* Reports routes - accessible to all authenticated users */}
              <Route
                path="/reports"
                element={
                  <ProtectedRoute>
                    <Reports />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/reports/:id"
                element={
                  <ProtectedRoute>
                    <ReportDetail />
                  </ProtectedRoute>
                }
              />
              {/* Report creation - hide from regular users */}
              <Route
                path="/report/new"
                element={
                  <ProtectedRoute excludeRoles={['user']}>
                    <ReportImpact />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/report/edit/:id"
                element={
                  <ProtectedRoute excludeRoles={['user']}>
                    <ReportImpact />
                  </ProtectedRoute>
                }
              />
              <Route path="/assistance/:id" element={<ReportDetail />} />
              <Route path="/assistance/received/:id" element={<ReportDetail />} />
              <Route path="/volunteer" element={<div className="p-8 text-center">Volunteer page coming soon...</div>} />
              
              {/* Admin routes */}
              <Route
                path="/admin/*"
                element={
                  <ProtectedRoute requiredRoles={['admin']}>
                    <AdminPanel />
                  </ProtectedRoute>
                }
              />
              
              {/* CJ and Admin routes */}
              <Route
                path="/verify-reports"
                element={
                  <ProtectedRoute requiredRoles={['admin', 'cj']}>
                    <div className="p-8 text-center">Verify Reports page coming soon...</div>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics"
                element={
                  <ProtectedRoute requiredRoles={['admin', 'cj']}>
                    <div className="p-8 text-center">Analytics page coming soon...</div>
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#4ade80',
                secondary: '#fff',
              },
            },
            error: {
              duration: 4000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
       </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App
