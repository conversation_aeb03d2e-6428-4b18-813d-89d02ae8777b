import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Search,
  Filter,
  MapPin,
  Calendar,
  User,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  X,
  SlidersHorizontal,
  ImageIcon,
  CheckCircle,
  Clock,
  AlertTriangle,
  Grid3X3,
  List,
  TrendingUp,
  Eye,
  Zap,
  Shield,
  Activity,
  Star,
  Download,
  Share2,
  Flame,
  Waves,
  Mountain,
  Wind,
  Droplets,
  Building,
  Truck,
  RefreshCw,
  BarChart3,
  Users,
  Sparkles,
  Heart,
  Globe2
} from 'lucide-react';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';

// Hooks
import { useAuth } from '../hooks/useAuth';
import { useRoles } from '../hooks/useRoles';

// Data
import { mockReports } from '../data/mockData';
import { Report } from '../types';

const Reports: React.FC = () => {
  // Auth and roles
  const { isAuthenticated } = useAuth();
  const { isAdmin, isCj, isOnlyUser } = useRoles();

  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDisasterType, setSelectedDisasterType] = useState<string>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showOnlyWithImages, setShowOnlyWithImages] = useState(false);
  const [sortBy, setSortBy] = useState<string>('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(false);

  // Role-based permissions
  const canViewAllReports = isAdmin() || isCj();
  const canViewSensitiveInfo = isAdmin() || isCj();
  const canManageReports = isAdmin();
  const isRegularUser = isOnlyUser();

  // Filter options
  const disasterTypes = [
    'all',
    'flood',
    'fire',
    'earthquake',
    'storm',
    'hurricane',
    'wildfire',
    'chemical_spill',
    'structural_failure',
    'infrastructure_failure',
    'other'
  ];
  const severityLevels = ['all', 'low', 'medium', 'high', 'critical'];
  // Status options based on user role
  const statusOptions = isRegularUser
    ? ['all', 'verified'] // Regular users only see verified reports
    : ['all', 'pending', 'verified', 'resolved']; // Admin/CJ see all statuses
  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'severity', label: 'Severity' },
    { value: 'location', label: 'Location' }
  ];

  // Filter and sort reports based on user role
  const filteredAndSortedReports = useMemo(() => {
    let filtered = mockReports.filter(report => {
      // Role-based filtering: Regular users only see verified reports
      if (isRegularUser && report.status !== 'verified') {
        return false;
      }

      // Search filter
      const matchesSearch = searchTerm === '' ||
        report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.location.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(searchTerm.toLowerCase());

      // Type filter
      const matchesType = selectedDisasterType === 'all' || report.disasterType === selectedDisasterType;

      // Severity filter
      const matchesSeverity = selectedSeverity === 'all' || report.severity === selectedSeverity;

      // Status filter
      const matchesStatus = selectedStatus === 'all' || report.status === selectedStatus;

      // Images filter
      const matchesImages = !showOnlyWithImages || (report.photos && report.photos.length > 0);

      return matchesSearch && matchesType && matchesSeverity && matchesStatus && matchesImages;
    });

    // Sort reports
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'severity':
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return severityOrder[b.severity] - severityOrder[a.severity];
        case 'location':
          return a.location.address.localeCompare(b.location.address);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchTerm, selectedDisasterType, selectedSeverity, selectedStatus, showOnlyWithImages, sortBy, isRegularUser]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedReports.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedReports = filteredAndSortedReports.slice(startIndex, startIndex + itemsPerPage);

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedDisasterType, selectedSeverity, selectedStatus, showOnlyWithImages, sortBy]);

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedDisasterType('all');
    setSelectedSeverity('all');
    setSelectedStatus('all');
    setShowOnlyWithImages(false);
    setSortBy('newest');
    setCurrentPage(1);
  };

  // Get disaster type icon component
  const getDisasterIcon = (type: string) => {
    const iconProps = { size: 16, className: "text-white" };
    switch (type) {
      case 'flood': return <Waves {...iconProps} />;
      case 'fire': return <Flame {...iconProps} />;
      case 'earthquake': return <Mountain {...iconProps} />;
      case 'storm': return <Wind {...iconProps} />;
      case 'hurricane': return <Wind {...iconProps} />;
      case 'wildfire': return <Flame {...iconProps} />;
      case 'chemical_spill': return <Droplets {...iconProps} />;
      case 'structural_failure': return <Building {...iconProps} />;
      case 'infrastructure_failure': return <Truck {...iconProps} />;
      default: return <AlertTriangle {...iconProps} />;
    }
  };

  // Get disaster type color
  const getDisasterColor = (type: string) => {
    switch (type) {
      case 'flood': return 'from-blue-500 to-blue-600';
      case 'fire': return 'from-red-500 to-orange-500';
      case 'earthquake': return 'from-amber-600 to-orange-600';
      case 'storm': return 'from-gray-500 to-gray-600';
      case 'hurricane': return 'from-purple-500 to-indigo-600';
      case 'wildfire': return 'from-red-600 to-red-700';
      case 'chemical_spill': return 'from-yellow-500 to-amber-500';
      case 'structural_failure': return 'from-slate-500 to-slate-600';
      case 'infrastructure_failure': return 'from-orange-500 to-red-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  // Get disaster type display name
  const getDisasterTypeName = (type: string) => {
    switch (type) {
      case 'flood': return 'Flood';
      case 'fire': return 'Fire';
      case 'earthquake': return 'Earthquake';
      case 'storm': return 'Storm';
      case 'hurricane': return 'Hurricane';
      case 'wildfire': return 'Wildfire';
      case 'chemical_spill': return 'Chemical Spill';
      case 'structural_failure': return 'Building Collapse';
      case 'infrastructure_failure': return 'Infrastructure';
      default: return 'Other';
    }
  };

  // Get severity color classes
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get default image for report type
  const getDefaultImage = (type: string) => {
    const defaultImages = {
      flood: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg',
      fire: 'https://images.pexels.com/photos/1112080/pexels-photo-1112080.jpeg',
      earthquake: 'https://images.pexels.com/photos/2166711/pexels-photo-2166711.jpeg',
      storm: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg',
      default: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg'
    };
    return defaultImages[type as keyof typeof defaultImages] || defaultImages.default;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/20">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        {/* Enhanced Page Header */}
        <div className="text-center mb-20">
          {/* Status Badges */}
          <div className="flex flex-wrap items-center justify-center gap-3 mb-10">
            <div className="group inline-flex items-center px-5 py-2.5 rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-200/50 text-blue-700 text-sm font-semibold hover:from-blue-500/20 hover:to-indigo-500/20 transition-all duration-300 backdrop-blur-sm">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2.5 animate-pulse"></div>
              <Activity size={16} className="mr-2" />
              Live Reports
            </div>
            <button
              onClick={() => {
                setIsLoading(true);
                setTimeout(() => {
                  window.location.reload();
                }, 500);
              }}
              className="group inline-flex items-center px-5 py-2.5 rounded-full bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-200/50 text-emerald-700 text-sm font-semibold hover:from-emerald-500/20 hover:to-green-500/20 transition-all duration-300 backdrop-blur-sm"
              title="Refresh Reports"
              disabled={isLoading}
            >
              <RefreshCw size={16} className={`mr-2 ${isLoading ? 'animate-spin' : 'group-hover:rotate-180'} transition-transform duration-500`} />
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
            <div className="inline-flex items-center px-5 py-2.5 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 text-purple-700 text-sm font-semibold backdrop-blur-sm">
              <Sparkles size={16} className="mr-2" />
              Enhanced UI
            </div>
          </div>

          {/* Main Title */}
          <div className="relative">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black text-gray-900 mb-6 leading-[0.9] tracking-tight">
              {isRegularUser ? 'Community' : 'Emergency'}
              <br />
              <span className="relative inline-block">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 via-orange-500 to-amber-500">
                  {isRegularUser ? 'Safety Updates' : 'Reports Hub'}
                </span>
                <div className="absolute -inset-1 bg-gradient-to-r from-red-500/20 via-orange-500/20 to-amber-500/20 blur-xl -z-10"></div>
              </span>
            </h1>

            {/* Floating decorative elements */}
            <div className="absolute -top-8 -left-8 w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -top-4 -right-12 w-12 h-12 bg-gradient-to-br from-pink-400 to-red-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
          </div>

          {/* Enhanced Description */}
          <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10 font-medium">
            {isRegularUser
              ? 'Stay informed about verified disaster reports and safety updates in your community. Access reliable information to keep yourself and your family safe.'
              : 'Real-time disaster reports from communities worldwide. Browse verified incidents, track emergency responses, and stay informed about ongoing situations in your area.'
            }
          </p>

          {/* Enhanced Quick Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-6xl mx-auto">
            <div className="group relative bg-white/90 backdrop-blur-md rounded-3xl p-6 border border-white/60 hover:border-blue-200/60 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-1 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:shadow-blue-500/25 group-hover:scale-110 transition-all duration-300">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-black text-blue-600 group-hover:scale-105 transition-transform duration-300">
                      {filteredAndSortedReports.length}
                    </div>
                    <div className="text-xs text-blue-500 font-medium">reports</div>
                  </div>
                </div>
                <div className="text-gray-700 text-sm font-semibold mb-1">Total Reports</div>
                <div className="text-xs text-gray-500">Currently displayed</div>
              </div>
            </div>

            <div className="group relative bg-white/90 backdrop-blur-md rounded-3xl p-6 border border-white/60 hover:border-emerald-200/60 hover:shadow-2xl hover:shadow-emerald-500/10 hover:-translate-y-1 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-lg group-hover:shadow-emerald-500/25 group-hover:scale-110 transition-all duration-300">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-black text-emerald-600 group-hover:scale-105 transition-transform duration-300">
                      {filteredAndSortedReports.filter(r => r.status === 'verified').length}
                    </div>
                    <div className="text-xs text-emerald-500 font-medium">verified</div>
                  </div>
                </div>
                <div className="text-gray-700 text-sm font-semibold mb-1">Verified</div>
                <div className="text-xs text-gray-500">
                  {filteredAndSortedReports.length > 0 ? Math.round((filteredAndSortedReports.filter(r => r.status === 'verified').length / filteredAndSortedReports.length) * 100) : 0}% of total
                </div>
              </div>
            </div>

            <div className="group relative bg-white/90 backdrop-blur-md rounded-3xl p-6 border border-white/60 hover:border-red-200/60 hover:shadow-2xl hover:shadow-red-500/10 hover:-translate-y-1 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-orange-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl shadow-lg group-hover:shadow-red-500/25 group-hover:scale-110 transition-all duration-300">
                    <AlertTriangle className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-black text-red-600 group-hover:scale-105 transition-transform duration-300">
                      {filteredAndSortedReports.filter(r => r.severity === 'critical' || r.severity === 'high').length}
                    </div>
                    <div className="text-xs text-red-500 font-medium">urgent</div>
                  </div>
                </div>
                <div className="text-gray-700 text-sm font-semibold mb-1">High Priority</div>
                <div className="text-xs text-gray-500">Critical & High severity</div>
              </div>
            </div>

            <div className="group relative bg-white/90 backdrop-blur-md rounded-3xl p-6 border border-white/60 hover:border-purple-200/60 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-1 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg group-hover:shadow-purple-500/25 group-hover:scale-110 transition-all duration-300">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-black text-purple-600 group-hover:scale-105 transition-transform duration-300">
                      {filteredAndSortedReports.filter(r => r.assistanceLog && r.assistanceLog.length > 0).length}
                    </div>
                    <div className="text-xs text-purple-500 font-medium">active</div>
                  </div>
                </div>
                <div className="text-gray-700 text-sm font-semibold mb-1">Active Response</div>
                <div className="text-xs text-gray-500">Reports with assistance</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Role-based Information Banner */}
        {isRegularUser && (
          <div className="relative bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-50 border border-blue-200/60 rounded-3xl p-8 mb-12 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="relative flex items-start space-x-4">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-blue-900 mb-3 flex items-center">
                  Community Safety View
                  <div className="ml-3 px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
                    Verified Only
                  </div>
                </h3>
                <p className="text-blue-700 leading-relaxed text-base">
                  You're viewing verified disaster reports and safety updates. Only confirmed incidents are shown to ensure reliable information for community safety.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Modernized Search and Filter Controls */}
        <div className="relative bg-white/95 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/40 p-8 mb-16 overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-purple-500/3"></div>
          <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full -translate-x-20 -translate-y-20"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-400/10 to-orange-400/10 rounded-full translate-x-16 translate-y-16"></div>

          <div className="relative">
            <div className="flex flex-col xl:flex-row gap-6 mb-8">
              {/* Premium Search Bar */}
              <div className="flex-1 relative group">
                <div className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-blue-500 transition-all duration-300 group-focus-within:scale-110">
                  <Search size={22} />
                </div>
                <input
                  type="text"
                  placeholder="Search disasters by location, type, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-14 pr-14 py-5 bg-gradient-to-r from-gray-50/90 to-white/90 backdrop-blur-sm border-2 border-gray-200/60 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300/80 transition-all duration-300 text-gray-800 placeholder-gray-500 font-medium text-lg shadow-inner"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="absolute right-5 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200"
                  >
                    <X size={18} />
                  </button>
                )}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>

              {/* Enhanced View Toggle */}
              <div className="flex items-center bg-gradient-to-r from-gray-100/80 to-gray-50/80 backdrop-blur-sm rounded-2xl p-1.5 shadow-inner border border-gray-200/50">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex items-center px-5 py-3 rounded-xl font-semibold transition-all duration-300 ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-lg shadow-blue-500/10 transform scale-105 border border-blue-100'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-white/70'
                  }`}
                >
                  <Grid3X3 size={18} className="mr-2" />
                  <span className="hidden sm:inline">Grid</span>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex items-center px-5 py-3 rounded-xl font-semibold transition-all duration-300 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-lg shadow-blue-500/10 transform scale-105 border border-blue-100'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-white/70'
                  }`}
                >
                  <List size={18} className="mr-2" />
                  <span className="hidden sm:inline">List</span>
                </button>
              </div>

              {/* Premium Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`group flex items-center px-6 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                  showFilters
                    ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 text-white shadow-xl shadow-blue-500/25'
                    : 'bg-gradient-to-r from-gray-100/80 to-gray-50/80 text-gray-700 hover:from-gray-200/80 hover:to-gray-100/80 shadow-lg border border-gray-200/50'
                }`}
              >
                <SlidersHorizontal size={20} className={`mr-3 ${showFilters ? '' : 'group-hover:rotate-180'} transition-transform duration-300`} />
                <span className="hidden sm:inline">Filters</span>
                {showFilters && <div className="ml-3 w-2 h-2 bg-white rounded-full animate-pulse"></div>}
              </button>

              {/* Enhanced Quick Actions */}
              <div className="flex items-center space-x-2">
                <button
                  className="group p-3.5 bg-gradient-to-r from-emerald-100/80 to-green-100/80 text-emerald-700 rounded-2xl hover:from-emerald-200/80 hover:to-green-200/80 transition-all duration-300 transform hover:scale-105 shadow-lg border border-emerald-200/50"
                  title="Export Reports"
                >
                  <Download size={18} className="group-hover:scale-110 transition-transform duration-200" />
                </button>
                <button
                  className="group p-3.5 bg-gradient-to-r from-purple-100/80 to-pink-100/80 text-purple-700 rounded-2xl hover:from-purple-200/80 hover:to-pink-200/80 transition-all duration-300 transform hover:scale-105 shadow-lg border border-purple-200/50"
                  title="Share Reports"
                >
                  <Share2 size={18} className="group-hover:scale-110 transition-transform duration-200" />
                </button>
                <button
                  className="group p-3.5 bg-gradient-to-r from-blue-100/80 to-indigo-100/80 text-blue-700 rounded-2xl hover:from-blue-200/80 hover:to-indigo-200/80 transition-all duration-300 transform hover:scale-105 shadow-lg border border-blue-200/50"
                  title="Global View"
                >
                  <Globe2 size={18} className="group-hover:scale-110 transition-transform duration-200" />
                </button>
              </div>
            </div>

            {/* Enhanced Filter Controls */}
            {showFilters && (
              <div className="border-t border-gray-200/60 pt-8 mt-8">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  {/* Disaster Type Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Disaster Type</label>
                    <select
                      value={selectedDisasterType}
                      onChange={(e) => setSelectedDisasterType(e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300 transition-all duration-200 text-gray-700 font-medium shadow-sm"
                    >
                      {disasterTypes.map(type => (
                        <option key={type} value={type}>
                          {type === 'all' ? 'All Types' : getDisasterTypeName(type)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Severity Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Severity Level</label>
                    <select
                      value={selectedSeverity}
                      onChange={(e) => setSelectedSeverity(e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300 transition-all duration-200 text-gray-700 font-medium shadow-sm"
                    >
                      {severityLevels.map(level => (
                        <option key={level} value={level}>
                          {level === 'all' ? 'All Severities' : level.charAt(0).toUpperCase() + level.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Status Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Report Status</label>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300 transition-all duration-200 text-gray-700 font-medium shadow-sm"
                    >
                      {statusOptions.map(status => (
                        <option key={status} value={status}>
                          {status === 'all' ? 'All Statuses' : status.charAt(0).toUpperCase() + status.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Sort By */}
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Sort Order</label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300 transition-all duration-200 text-gray-700 font-medium shadow-sm"
                    >
                      {sortOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Enhanced Additional Options */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 pt-6 border-t border-gray-200/60">
                  <div className="flex flex-wrap items-center gap-4">
                    <label className="group flex items-center cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          checked={showOnlyWithImages}
                          onChange={(e) => setShowOnlyWithImages(e.target.checked)}
                          className="sr-only"
                        />
                        <div className={`w-5 h-5 rounded-md border-2 transition-all duration-200 ${
                          showOnlyWithImages
                            ? 'bg-blue-500 border-blue-500'
                            : 'border-gray-300 group-hover:border-gray-400'
                        }`}>
                          {showOnlyWithImages && (
                            <CheckCircle size={12} className="text-white absolute inset-0.5" />
                          )}
                        </div>
                      </div>
                      <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">
                        Show only reports with images
                      </span>
                    </label>
                  </div>

                  <button
                    onClick={clearAllFilters}
                    className="group flex items-center px-5 py-2.5 text-sm font-medium text-gray-600 hover:text-gray-800 bg-gray-100/80 hover:bg-gray-200/80 rounded-xl transition-all duration-200 border border-gray-200/60"
                  >
                    <X size={16} className="mr-2 group-hover:rotate-90 transition-transform duration-200" />
                    Clear all filters
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Results Summary */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-12 bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40">
          <div className="mb-4 sm:mb-0">
            <p className="text-gray-700 font-medium">
              Showing <span className="font-bold text-blue-600">{startIndex + 1}</span>-<span className="font-bold text-blue-600">{Math.min(startIndex + itemsPerPage, filteredAndSortedReports.length)}</span> of <span className="font-bold text-blue-600">{filteredAndSortedReports.length}</span> reports
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {filteredAndSortedReports.length === mockReports.length ? 'All reports displayed' : 'Filtered results'}
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-600">Items per page:</label>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="px-4 py-2 bg-white border-2 border-gray-200 rounded-xl text-sm font-medium focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 hover:border-gray-300 transition-all duration-200 shadow-sm"
            >
              <option value={12}>12</option>
              <option value={24}>24</option>
              <option value={48}>48</option>
            </select>
          </div>
        </div>

        {/* Enhanced Reports Display */}
        {paginatedReports.length > 0 ? (
          viewMode === 'grid' ? (
            /* Premium Grid View */
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8 mb-16">
              {paginatedReports.map((report, index) => (
                <div
                  key={report.id}
                  className="group relative bg-white/95 backdrop-blur-md rounded-3xl shadow-lg border border-white/60 overflow-hidden hover:shadow-2xl hover:shadow-blue-500/10 hover:scale-[1.02] hover:-translate-y-1 transition-all duration-500 flex flex-col h-full"
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animation: 'fadeInUp 0.6s ease-out forwards'
                  }}
                >
                  {/* Enhanced Image Section */}
                  <div className="relative h-52 overflow-hidden">
                    <img
                      src={report.photos?.[0] || getDefaultImage(report.disasterType)}
                      alt={report.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      loading="lazy"
                    />
                    {/* Enhanced gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/20"></div>

                    {/* Premium Severity Badge */}
                    <div className={`absolute top-4 left-4 px-3 py-1.5 rounded-full text-xs font-bold text-white shadow-lg backdrop-blur-sm ${getSeverityColor(report.severity)} border border-white/20`}>
                      {report.severity?.toUpperCase()}
                    </div>

                    {/* Enhanced Status Badge */}
                    <div className={`absolute top-4 right-4 px-3 py-1.5 rounded-full text-xs font-semibold shadow-lg backdrop-blur-sm border border-white/20 ${
                      report.status === 'verified' ? 'bg-emerald-100/90 text-emerald-800' :
                      report.status === 'pending' ? 'bg-amber-100/90 text-amber-800' :
                      'bg-gray-100/90 text-gray-800'
                    }`}>
                      {report.status === 'verified' && <CheckCircle size={12} className="inline mr-1" />}
                      {report.status === 'pending' && <Clock size={12} className="inline mr-1" />}
                      {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                    </div>

                    {/* Enhanced Multiple Images Indicator */}
                    {report.photos && report.photos.length > 1 && (
                      <div className="absolute bottom-4 right-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1.5 rounded-xl text-xs flex items-center shadow-lg border border-white/20">
                        <ImageIcon size={12} className="mr-1.5" />
                        {report.photos.length}
                      </div>
                    )}

                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-blue-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  {/* Enhanced Content Section */}
                  <div className="p-6 flex flex-col flex-1">
                    {/* Main Content - grows to fill available space */}
                    <div className="flex-1">
                      <div className="mb-4">
                        <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2 leading-tight">
                          {report.title}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                          {report.description}
                        </p>
                      </div>

                      {/* Enhanced Meta Information */}
                      <div className="space-y-3 mb-5">
                        <div className="flex items-center text-sm text-gray-600">
                          <div className="p-1.5 bg-blue-100 rounded-lg mr-3">
                            <MapPin size={14} className="text-blue-600" />
                          </div>
                          <span className="truncate font-medium">{report.location.address}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <div className="p-1.5 bg-emerald-100 rounded-lg mr-3">
                            <Calendar size={14} className="text-emerald-600" />
                          </div>
                          <span className="font-medium">{format(report.createdAt, 'MMM d, yyyy')}</span>
                        </div>
                        {canViewSensitiveInfo && (
                          <div className="flex items-center text-sm text-gray-600">
                            <div className="p-1.5 bg-purple-100 rounded-lg mr-3">
                              <User size={14} className="text-purple-600" />
                            </div>
                            <span className="font-medium">{report.reporterName}</span>
                          </div>
                        )}
                      </div>

                      {/* Enhanced Disaster Type Badge */}
                      <div className="mb-5">
                        <span className={`inline-flex items-center px-4 py-2 rounded-2xl text-sm font-bold bg-gradient-to-r ${getDisasterColor(report.disasterType)} text-white shadow-lg hover:shadow-xl transition-shadow duration-300`}>
                          {getDisasterIcon(report.disasterType)}
                          <span className="ml-2">{getDisasterTypeName(report.disasterType)}</span>
                        </span>
                      </div>
                    </div>

                    {/* Enhanced Action Button - always at bottom */}
                    <div className="mt-auto">
                      <Link
                        to={`/reports/${report.id}`}
                        className="group w-full bg-gradient-to-r from-blue-600 via-blue-700 to-blue-600 text-white py-3.5 px-5 rounded-2xl font-semibold hover:from-blue-700 hover:via-blue-800 hover:to-blue-700 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl flex items-center justify-center relative overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                        <span className="relative">View Details</span>
                        <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform relative" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Enhanced List View */
            <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-xl border border-white/60 overflow-hidden mb-16">
              <div className="divide-y divide-gray-100/80">
                {paginatedReports.map((report, index) => (
                  <div
                    key={report.id}
                    className="group hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300"
                    style={{
                      animationDelay: `${index * 30}ms`,
                      animation: 'fadeInUp 0.4s ease-out forwards'
                    }}
                  >
                    <div className="p-6 lg:p-8 flex items-center space-x-6">
                      {/* Enhanced Thumbnail */}
                      <div className="relative w-24 h-24 rounded-2xl overflow-hidden flex-shrink-0 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <img
                          src={report.photos?.[0] || getDefaultImage(report.disasterType)}
                          alt={report.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          loading="lazy"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10"></div>
                      </div>

                      {/* Enhanced Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0 pr-4">
                            <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 line-clamp-1">
                              {report.title}
                            </h3>
                            <p className="text-gray-600 text-base leading-relaxed line-clamp-2 mb-3">
                              {report.description}
                            </p>
                          </div>

                          {/* Enhanced Status and Severity Badges */}
                          <div className="flex flex-col items-end space-y-2 ml-4">
                            <span className={`px-3 py-1.5 rounded-xl text-xs font-bold text-white shadow-md ${getSeverityColor(report.severity)} border border-white/20`}>
                              {report.severity?.toUpperCase()}
                            </span>
                            <span className={`px-3 py-1.5 rounded-xl text-xs font-semibold shadow-md border border-white/20 ${
                              report.status === 'verified' ? 'bg-emerald-100/90 text-emerald-800' :
                              report.status === 'pending' ? 'bg-amber-100/90 text-amber-800' :
                              'bg-gray-100/90 text-gray-800'
                            }`}>
                              {report.status === 'verified' && <CheckCircle size={12} className="inline mr-1" />}
                              {report.status === 'pending' && <Clock size={12} className="inline mr-1" />}
                              {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                            </span>
                          </div>
                        </div>

                        {/* Enhanced Meta Information */}
                        <div className="flex flex-wrap items-center gap-4 mt-4">
                          <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-xl">
                            <MapPin size={14} className="mr-2 text-blue-600" />
                            <span className="text-sm font-medium text-blue-800 truncate max-w-48">{report.location.address}</span>
                          </div>
                          <div className="flex items-center bg-emerald-50 px-3 py-1.5 rounded-xl">
                            <Calendar size={14} className="mr-2 text-emerald-600" />
                            <span className="text-sm font-medium text-emerald-800">{format(report.createdAt, 'MMM d, yyyy')}</span>
                          </div>
                          {canViewSensitiveInfo && (
                            <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-xl">
                              <User size={14} className="mr-2 text-purple-600" />
                              <span className="text-sm font-medium text-purple-800">{report.reporterName}</span>
                            </div>
                          )}
                          <span className={`inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-bold bg-gradient-to-r ${getDisasterColor(report.disasterType)} text-white shadow-lg`}>
                            {getDisasterIcon(report.disasterType)}
                            <span className="ml-2">{getDisasterTypeName(report.disasterType)}</span>
                          </span>
                          {report.photos && report.photos.length > 1 && (
                            <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-xl">
                              <ImageIcon size={14} className="mr-2 text-gray-600" />
                              <span className="text-sm font-medium text-gray-800">{report.photos.length} photos</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Action Button */}
                      <div className="flex-shrink-0">
                        <Link
                          to={`/reports/${report.id}`}
                          className="group bg-gradient-to-r from-blue-600 via-blue-700 to-blue-600 text-white px-8 py-3 rounded-2xl font-semibold hover:from-blue-700 hover:via-blue-800 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center relative overflow-hidden"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                          <span className="relative">View Details</span>
                          <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform relative" />
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        ) : (
          /* Enhanced Empty State */
          <div className="text-center py-24">
            <div className="max-w-2xl mx-auto">
              <div className="relative mb-12">
                <div className="bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100 rounded-full w-40 h-40 flex items-center justify-center mx-auto shadow-2xl">
                  <Search size={56} className="text-blue-500" />
                </div>
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
              </div>

              <h3 className="text-4xl font-black text-gray-900 mb-6">No Reports Found</h3>
              <p className="text-xl text-gray-600 mb-12 leading-relaxed">
                No disaster reports match your current search criteria. Try adjusting your filters or search terms to discover relevant reports.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <button
                  onClick={clearAllFilters}
                  className="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-10 py-4 rounded-2xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 font-semibold transform hover:scale-105 shadow-xl hover:shadow-2xl relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  <span className="relative">Clear All Filters</span>
                </button>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="bg-white text-gray-700 px-10 py-4 rounded-2xl hover:bg-gray-50 transition-all duration-300 font-semibold transform hover:scale-105 shadow-lg border-2 border-gray-200 hover:border-gray-300"
                >
                  Adjust Filters
                </button>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/60 shadow-lg">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">Current Search Criteria:</h4>
                <div className="flex flex-wrap gap-3 justify-center">
                  <span className="px-4 py-2 bg-blue-100 text-blue-800 rounded-xl text-sm font-medium">
                    Search: {searchTerm || 'All reports'}
                  </span>
                  {selectedDisasterType !== 'all' && (
                    <span className="px-4 py-2 bg-orange-100 text-orange-800 rounded-xl text-sm font-medium">
                      Type: {getDisasterTypeName(selectedDisasterType)}
                    </span>
                  )}
                  {selectedSeverity !== 'all' && (
                    <span className="px-4 py-2 bg-red-100 text-red-800 rounded-xl text-sm font-medium">
                      Severity: {selectedSeverity.charAt(0).toUpperCase() + selectedSeverity.slice(1)}
                    </span>
                  )}
                  {selectedStatus !== 'all' && (
                    <span className="px-4 py-2 bg-green-100 text-green-800 rounded-xl text-sm font-medium">
                      Status: {selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Premium Pagination */}
        {totalPages > 1 && (
          <div className="bg-white/95 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/60 p-8 mb-12 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-purple-500/3"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full translate-x-16 -translate-y-16"></div>

            <div className="relative">
              {/* Enhanced Pagination Info */}
              <div className="flex flex-col lg:flex-row items-center justify-between mb-10">
                <div className="mb-6 lg:mb-0">
                  <p className="text-lg text-gray-700 font-semibold mb-2">
                    Showing <span className="font-black text-blue-600 text-xl">{startIndex + 1}</span> to{' '}
                    <span className="font-black text-blue-600 text-xl">
                      {Math.min(startIndex + itemsPerPage, filteredAndSortedReports.length)}
                    </span>{' '}
                    of <span className="font-black text-blue-600 text-xl">{filteredAndSortedReports.length}</span> reports
                  </p>
                  <p className="text-sm text-gray-500">
                    Browse through all available disaster reports
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-lg text-gray-700 font-semibold">
                    Page <span className="font-black text-blue-600 text-xl">{currentPage}</span> of{' '}
                    <span className="font-black text-blue-600 text-xl">{totalPages}</span>
                  </div>
                  <div className="h-8 w-px bg-gray-300"></div>
                  <div className="text-sm text-gray-500">
                    {Math.ceil((currentPage / totalPages) * 100)}% complete
                  </div>
                </div>
              </div>

              {/* Enhanced Pagination Controls */}
              <div className="flex flex-wrap items-center justify-center gap-3">
                {/* Previous Button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`group flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
                      : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  <ChevronLeft size={18} className={`mr-2 ${currentPage === 1 ? '' : 'group-hover:-translate-x-1'} transition-transform`} />
                  Previous
                </button>

                {/* Enhanced Page Numbers */}
                <div className="flex items-center space-x-2">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`w-12 h-12 rounded-2xl text-sm font-bold transition-all duration-300 transform hover:scale-110 ${
                          currentPage === pageNum
                            ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg shadow-blue-500/25 border-2 border-blue-500'
                            : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 shadow-md hover:shadow-lg'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                {/* Next Button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`group flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
                      : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  Next
                  <ChevronRight size={18} className={`ml-2 ${currentPage === totalPages ? '' : 'group-hover:translate-x-1'} transition-transform`} />
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default Reports;
