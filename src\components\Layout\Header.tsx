import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ShieldCheck, Menu, X, ChevronDown, User, LogOut, Settings, Shield, Heart, Globe } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useRoles } from '../../hooks/useRoles';
import Avatar from '../Common/Avatar';

interface NavItem {
  name: string;
  path: string;
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const location = useLocation();
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout } = useAuth();
  const { isAdmin, isCj, hasAdminOrCjRole, isOnlyUser, formatRoleName } = useRoles();

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setIsUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getNavItems = (): NavItem[] => {
    // Base navigation items for all users - matching reference design
    const baseItems = [
      { name: 'Home', path: '/' },
      { name: 'About', path: '/about' },
      { name: 'What We Do', path: '/what-we-do' },
      { name: 'Get Involved', path: '/get-involved' },
      { name: 'News', path: '/news' },
      { name: 'Resources', path: '/resources' },
      { name: 'Contact', path: '/contact' },
    ];

    // Add "Report" only for non-regular users (hide from users with only 'user' role)
    if (isAuthenticated && !isOnlyUser()) {
      baseItems.splice(1, 0, { name: 'Report', path: '/report/new' });
    }

    // Add "View Reports" for all authenticated users
    if (isAuthenticated) {
      baseItems.push({ name: 'View Reports', path: '/reports' });
    }

    // Add role-based navigation items for authenticated users
    if (isAuthenticated) {
      const authItems = [];

      // Add Dashboard for non-admin users
      if (!isOnlyUser() && !isAdmin()) {
        authItems.push({ name: 'Dashboard', path: '/dashboard' });
      }

      // Add Admin-only items
      if (isAdmin()) {
        authItems.push({ name: 'Admin Panel', path: '/admin' });
      }

      return [...baseItems, ...authItems];
    }

    return baseItems;
  };

  const isActivePage = (path: string): boolean => {
    return location.pathname === path;
  };

  const handleLogout = () => {
    logout();
    setIsUserDropdownOpen(false);
    // Redirect will be handled by the auth system
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      {/* Top Bar with Language and Donate */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-10 text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Globe size={14} className="text-gray-500" />
                <span className="text-gray-600">EN</span>
                <ChevronDown size={12} className="text-gray-500" />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/donate"
                className="bg-red-600 text-white px-4 py-1 rounded-full text-xs font-medium hover:bg-red-700 transition-colors flex items-center"
              >
                <Heart size={12} className="mr-1" />
                Donate Now
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl shadow-sm">
              <ShieldCheck size={24} />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">GDRC</h1>
              <p className="text-xs text-gray-500 -mt-1">Global Disaster Response Center</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {getNavItems().map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-50 ${
                  isActivePage(item.path)
                    ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-600'
                    : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Auth Section */}
          <div className="hidden lg:flex items-center space-x-4">
            {isAuthenticated ? (
              /* User Profile Dropdown */
              <div className="relative" ref={userDropdownRef}>
                <button
                  onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200"
                >
                  <Avatar
                    src={user?.photoUrl}
                    alt={user?.name}
                    name={user?.name}
                    size="sm"
                  />
                  <div className="text-left hidden xl:block">
                    <div className="text-sm font-medium text-gray-900">{user?.name}</div>
                    <div className="text-xs text-gray-500">
                      {user?.roles?.filter(role => role).map(role => formatRoleName(role)).join(', ') || 'User'}
                    </div>
                  </div>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${
                      isUserDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {/* User Dropdown Menu */}
                {isUserDropdownOpen && (
                  <div className="absolute top-full right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <div className="text-sm font-medium text-gray-900">{user?.name}</div>
                      <div className="text-xs text-gray-500">{user?.email}</div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {user?.roles?.filter(role => role).map(role => (
                          <span
                            key={role}
                            className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                              role === 'admin' ? 'bg-red-100 text-red-800' :
                              role === 'cj' ? 'bg-blue-100 text-blue-800' :
                              'bg-green-100 text-green-800'
                            }`}
                          >
                            {formatRoleName(role)}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    {!isOnlyUser() && !isAdmin() && (
                      <Link
                        to="/dashboard"
                        onClick={() => setIsUserDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <User size={16} className="mr-3 text-gray-400" />
                        Dashboard
                      </Link>
                    )}
                    
                    {isAdmin() && (
                      <Link
                        to="/admin/roles"
                        onClick={() => setIsUserDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <Shield size={16} className="mr-3 text-gray-400" />
                        Role Management
                      </Link>
                    )}
                    
                    {hasAdminOrCjRole() && (
                      <Link
                        to="/admin/settings"
                        onClick={() => setIsUserDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <Settings size={16} className="mr-3 text-gray-400" />
                        Settings
                      </Link>
                    )}
                    
                    <hr className="my-1 border-gray-200" />
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <LogOut size={16} className="mr-3 text-gray-400" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              /* Login Button */
              <Link
                to="/login"
                className="flex items-center space-x-1 px-4 py-2 text-gray-700 hover:text-blue-600 transition-colors font-medium text-sm"
              >
                <User size={16} />
                <span>Login</span>
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-100 shadow-lg">
          <div className="px-4 py-6 space-y-2">
            {getNavItems().map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                  isActivePage(item.path)
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}



            {/* Mobile Auth Section */}
            <div className="pt-4 border-t border-gray-100">
              {isAuthenticated ? (
                /* Mobile User Profile */
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <Avatar
                      src={user?.photoUrl}
                      alt={user?.name}
                      name={user?.name}
                      size="lg"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{user?.name}</div>
                      <div className="text-sm text-gray-500">{user?.email}</div>
                    </div>
                  </div>

                  {!isOnlyUser() && !isAdmin() && (
                    <Link
                      to="/dashboard"
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 transition-colors py-2"
                    >
                      <User size={20} />
                      <span className="text-lg font-medium">Dashboard</span>
                    </Link>
                  )}

                  <button
                    onClick={() => {
                      handleLogout();
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center space-x-3 text-gray-600 hover:text-red-600 transition-colors py-2 w-full text-left"
                  >
                    <LogOut size={20} />
                    <span className="text-lg font-medium">Logout</span>
                  </button>
                </div>
              ) : (
                /* Mobile Login and Donate Buttons */
                <div className="space-y-3">
                  <Link
                    to="/login"
                    className="flex items-center justify-center space-x-2 w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <User size={18} />
                    <span>Login</span>
                  </Link>
                  <Link
                    to="/donate"
                    className="flex items-center justify-center space-x-2 w-full bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-semibold"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Heart size={18} />
                    <span>Donate Now</span>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
