@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet Map Fixes */
.leaflet-container {
  height: 100% !important;
  width: 100% !important;
  font-family: inherit !important;
}

.leaflet-control-container {
  font-family: inherit !important;
}

.leaflet-popup-content-wrapper {
  font-family: inherit !important;
}

/* Fix for Leaflet marker icons */
.leaflet-default-icon-path {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png');
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  html {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  * {
    border-color: hsl(var(--border));
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  }

  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Enhanced animations for Reports page */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  /* Text clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Leaflet map styles to ensure proper zoom functionality and z-index layering */
.leaflet-container {
  touch-action: pan-x pan-y !important;
  /* Ensure map container stays below header (z-50 = 50) */
  z-index: 10 !important;
}

/* Control all Leaflet elements to stay below header */
.leaflet-control-container {
  z-index: 15 !important;
}

.leaflet-control {
  z-index: 15 !important;
}

.leaflet-popup-pane {
  z-index: 20 !important;
}

.leaflet-tooltip-pane {
  z-index: 25 !important;
}

.leaflet-marker-pane {
  z-index: 12 !important;
}

.leaflet-tile-pane {
  z-index: 5 !important;
}

.leaflet-container .leaflet-control-zoom {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  z-index: 15 !important;
}

.leaflet-container .leaflet-control-zoom a {
  border-radius: 6px !important;
  border: none !important;
  background-color: white !important;
  color: #374151 !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.leaflet-container .leaflet-control-zoom a:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

/* Ensure map sections don't interfere with header */
.map-section {
  position: relative;
  z-index: 1;
}

/* Ensure header always stays on top */
header {
  position: sticky !important;
  z-index: 50 !important;
}

/* Custom Leaflet popup styles */
.custom-disaster-popup .leaflet-popup-content-wrapper {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 0 !important;
}

.custom-disaster-popup .leaflet-popup-content {
  margin: 0 !important;
  line-height: 1.4 !important;
}

.custom-disaster-popup .leaflet-popup-tip {
  background: white !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Custom Leaflet tooltip styles */
.custom-disaster-tooltip {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.custom-disaster-tooltip::before {
  border-top-color: rgba(0, 0, 0, 0.8) !important;
}

/* Pulsing animation for critical disasters */
@keyframes pulse-marker {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

.pulsing-marker {
  animation: pulse-marker 2s infinite !important;
}
